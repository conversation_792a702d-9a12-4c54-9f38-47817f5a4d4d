package main

import (
	"database/sql"
	"fmt"
	_ "log"
	"os"

	"github.com/joho/godotenv"

	_ "github.com/mattn/go-sqlite3"
)

type db struct {
	dbtype       string
	dbname       string
	tablename    string
	DbConnection *sql.DB
}

func (d db) DBinitFromenv(tablename string) {
	err := godotenv.Load(".env")
	if err != nil {
		fmt.Println(err)
	}
	d.dbtype = os.Getenv("DB")
	d.dbname = os.Getenv("DBNAME")
	d.tablename = tablename
}

func (d db) OpenDB() error {
	DbConnection, err := sql.Open(d.dbtype, d.dbname)
	if err == nil {
		return err
	}
	d.DbConnection = DbConnection
	return nil
}

func main() {
	d  := db{}
	d.DBinitFromenv("user")
	d.OpenDB()

	
}
